{"compilerOptions": {"preserveSymlinks": false, "target": "esnext", "module": "esnext", "outDir": "dist", "allowJs": true, "strict": false, "jsx": "preserve", "moduleResolution": "node", "skipLibCheck": true, "esModuleInterop": false, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "useDefineForClassFields": true, "declaration": false, "sourceMap": true, "packages": ["esnext", "dom", "dom.iterable", "scripthost"], "baseUrl": ".", "types": ["node", "vite/client"], "paths": {"@/*": ["src/*"], "@c/*": ["src/components/*"], "@v/*": ["src/views/*"], "@mh-base/core": ["../../mh-base/packages/Base/Core"]}}, "include": ["../../mh-base/packages/Base/Core/src/**/*.ts", "../../mh-base/packages/Base/Core/src/**/*.d.ts", "../../mh-base/packages/Base/Core/src/**/*.tsx", "../../mh-base/packages/Base/Core/src/**/*.vue", "src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue"], "exclude": ["**/dist"], "references": [{"path": "./tsconfig.node.json"}]}