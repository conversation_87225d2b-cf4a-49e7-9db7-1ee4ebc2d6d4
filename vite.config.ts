import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import autoImport from 'unplugin-auto-import/vite'
import { defineConfig, loadEnv } from 'vite'
import { lazyImport } from 'vite-plugin-lazy-import'


export default defineConfig(async ({ command, mode }: any): Promise<any> => {
  const env = loadEnv(mode, process.cwd(), '')
  const { VITE_APP_ALIAS = '' } = env

  console.info(resolve(__dirname, '.', 'src'))

  const { default: unocss } = await import('unocss/vite')

  return {
    plugins: [
      vue({ script: { defineModel: true } }),
      lazyImport({ resolvers: [] }),
      unocss(),
      autoImport({
        imports: [
          'vue',
          'vue-router',
          { '@idmy/core': ['cfg', 'sleep', 'useLoading', 'useCache', 'http', 'newError', 'Dialog', 'Modal', 'Message', 'dayjs', 'Data', 'back', 'to', 'currentRouter', 'currentRoute', 'emitter'] },
          { 'lodash-es': ['isNil', 'isString', 'isArray', 'cloneDeep'] },
        ],
        dts: 'auto-imports.d.ts',
        defaultExportByFilename: true,
        vueTemplate: true,
      }),
    ],
    css: {
      preprocessorOptions: {
        less: {
          modifyVars: {
            'color-text-4': 'var(--color-neutral-6)',
          },
          javascriptEnabled: true,
        },
      },
    },
    base: `/${VITE_APP_ALIAS}`,
    build: {
      target: 'es2015',
      outDir: `dist/${VITE_APP_ALIAS}`,
      rollupOptions: {
        external: ['vue', 'ant-design-vue', '@ant-design/icons-vue', 'lodash-es', '@idmy/core', '@idmy/antd'],
        plugins: [],
      },
    },
    server: {
      host: '0.0.0.0',
      port: 8002,
    },
    resolve: {
      alias: {
        '@': resolve(__dirname, '.', 'src'),
        '@c': resolve(__dirname, '.', 'src/components'),
        '@v': resolve(__dirname, '.', 'src/views'),
        'lodash': resolve(__dirname, '.', 'node_modules/lodash-es'),
        '@mh-base/core': 'D:\\vue\\mh\\mh-base\\packages\\Base\\Core',
      },
    },
    assetsDir: '',
  }
})
