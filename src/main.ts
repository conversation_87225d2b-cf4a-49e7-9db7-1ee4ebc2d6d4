import 'uno.css'
import './config'
import 'ant-design-vue/dist/reset.css'
import DmyAntd from '@idmy/antd'
import '@mh-base/core/index.css'
import { initialized } from '@idmy/core'
import Antd from 'ant-design-vue'
import { createApp } from 'vue'
import App from './App.vue'
import routerFn from './router'


const app = createApp(App)
app.use(Antd)
app.use(DmyAntd)
initialized(app, () => {
  const router = routerFn()
  app.use(router)
  router.isReady().then(() => app.mount('#app'))
})
