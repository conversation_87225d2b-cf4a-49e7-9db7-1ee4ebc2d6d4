/**
 * 将 .env 的配置注入到 @idmy/core 中
 */
import { appConfig, httpConfig, loginConfig, rbacConfig } from '@idmy/core'
// @ts-ignore
import { version } from '../../package.json'
import 'ant-design-vue/dist/reset.css'
import '@mh-base/core/index.css'
import '@idmy/antd/dist/index.css'
import '@mh-base/core'

/** httpConfig* */
httpConfig.isCache = false
/** authConfig* */
rbacConfig.root = import.meta.env.VITE_ROOT === 'true'
rbacConfig.permission = import.meta.env.VITE_PERMISSION === 'true'
rbacConfig.role = import.meta.env.VITE_ROLE === 'true'
/** appConfig* */
appConfig.env = import.meta.env.MODE === 'develop.local' ? 'dev' : import.meta.env.MODE
appConfig.security.encrypt = import.meta.env.VITE_SECURITY_ENCRYPT === 'true'

appConfig.security.secret = import.meta.env.VITE_SECURITY_SECRET
appConfig.app.key = import.meta.env.VITE_APP_KEY
appConfig.app.title = import.meta.env.VITE_APP_TITLE
appConfig.app.alias = import.meta.env.VITE_APP_ALIAS ?? ''
appConfig.app.version = parseInt(version.replaceAll(/\D/g, ''))
appConfig.app.keys = {
  oauth: import.meta.env.VITE_APP_URL_oauth,
  idm: import.meta.env.VITE_APP_URL_idm,
  amc: import.meta.env.VITE_APP_URL_amc,
  bcs: import.meta.env.VITE_APP_URL_bcs,
  hip: import.meta.env.VITE_APP_URL_hip,
  hsd: import.meta.env.VITE_APP_URL_hsd,
  eam: import.meta.env.VITE_APP_URL_eam,
}
/** loginConfig* */
loginConfig.loginUrl = import.meta.env.VITE_LOGIN_URL
/** numberConfig* */
appConfig.number.currencyUnitValue = 1
